module.exports = {
  mongo: [
    {
      host: process.env.MONGO_HOST || 'mongodb',
      port: process.env.MONGO_PORT || 27017
    }
  ],
  redis: [
    {
      host: process.env.REDIS_HOST || 'redis',
      port: process.env.REDIS_PORT || 6379,
      password: process.env.REDIS_PASSWORD || 'your-secure-redis-password'
    }
  ],
  // Add Docker detection
  isDocker: process.env.RUNNING_IN_DOCKER === 'true' || process.env.NODE_ENV === 'production'
};
