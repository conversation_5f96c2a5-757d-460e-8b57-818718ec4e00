console.log('Starting debug test...');

try {
  console.log('1. Testing basic requires...');
  const fs = require('fs');
  const path = require('path');
  const express = require('express');
  console.log('✓ Basic requires OK');

  console.log('2. Testing config...');
  const config = require('./config/config');
  console.log('✓ Config OK');

  console.log('3. Testing logger...');
  require('./config/logger')();
  console.log('✓ Logger OK');

  console.log('4. Testing winston...');
  const winston = require('winston');
  winston.info('Winston test');
  console.log('✓ Winston OK');

  console.log('5. Testing mongo config...');
  const mongoConfig = require('./config/mongo');
  console.log('✓ Mongo config loaded');

  console.log('6. Testing redis config...');
  const redisConfig = require('./config/redis');
  console.log('✓ Redis config loaded');

  console.log('7. Testing analytics config...');
  const analyticsConfig = require('./config/analytics');
  console.log('✓ Analytics config loaded');

  console.log('8. Testing setup config...');
  const setupConfig = require('./config/setup');
  console.log('✓ Setup config loaded');

  console.log('9. Testing middlewares...');
  const middlewares = require('./middlewares');
  console.log('✓ Middlewares loaded');

  console.log('10. Testing routes...');
  const routes = require('./routes');
  console.log('✓ Routes loaded');

  console.log('All modules loaded successfully!');

} catch (error) {
  console.error('Error occurred:', error);
  console.error('Stack:', error.stack);
}
