{"name": "ezybackend-accounts-ui", "version": "2.0.0", "description": "EzyBackend Accounts UI - User account management interface", "main": "server.js", "license": "Apache-2.0", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "https://github.com/Ji<PERSON>erkumar2030/ezybackend.git"}, "engines": {"node": ">=20.0.0", "npm": ">=10.0.0"}, "scripts": {"start": "node server.js", "dev": "NODE_ENV=development webpack serve --mode development", "build": "NODE_ENV=production webpack --mode production", "build-dev": "NODE_ENV=development webpack --mode development", "watch": "NODE_ENV=development webpack --mode development --watch", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint . --ext .js,.jsx --fix", "lint-check": "eslint . --ext .js,.jsx", "format": "prettier --write \"**/*.{js,jsx,json,md,css,scss}\"", "format-check": "prettier --check \"**/*.{js,jsx,json,md,css,scss}\"", "prepare": "husky install || true"}, "dependencies": {"@mui/material": "^6.1.9", "@mui/icons-material": "^6.1.9", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "axios": "^1.10.0", "card-validator": "^10.0.0", "ejs": "^3.1.10", "express": "^5.1.0", "history": "^5.3.0", "lodash": "^4.17.21", "prop-types": "^15.8.1", "react": "^18.3.1", "react-cookie": "^7.2.2", "react-dom": "^18.3.1", "react-router": "^6.28.0", "react-router-dom": "^6.28.0", "react-transition-group": "^4.4.5"}, "devDependencies": {"@babel/core": "^7.25.0", "@babel/preset-env": "^7.25.0", "@babel/preset-react": "^7.25.0", "@babel/plugin-proposal-class-properties": "^7.18.6", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.1.0", "@testing-library/user-event": "^14.5.2", "babel-loader": "^9.2.1", "css-loader": "^7.1.2", "eslint": "^9.30.1", "eslint-config-airbnb": "^19.0.4", "eslint-plugin-import": "^2.32.0", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-react": "^7.37.2", "eslint-plugin-react-hooks": "^5.0.0", "html-webpack-plugin": "^5.6.3", "husky": "^9.1.7", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "prettier": "^3.4.2", "style-loader": "^4.0.0", "webpack": "^5.99.9", "webpack-cli": "^6.0.1", "webpack-dev-server": "^5.2.2"}}