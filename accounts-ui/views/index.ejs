<!DOCTYPE html>
<html lang = "en">

   <head>
      <meta charset = "UTF-8">
      <title>EzyBackend | Accounts</title>
      <link rel="shortcut icon" href="public/assets/images/icon.png">
      <link href="https://code.ionicframework.com/ionicons/2.0.1/css/ionicons.min.css"/>
      <link href="https://fonts.googleapis.com/css?family=Roboto:400,100,100italic,300,300italic,400italic,500,500italic,700,700italic" rel="stylesheet" type="text/css">
      <link rel="stylesheet" type="text/css" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.6/css/bootstrap.min.css">
      <link rel="stylesheet" type="text/css" href="https://maxcdn.bootstrapcdn.com/font-awesome/4.7.0/css/font-awesome.min.css">
      <link rel="stylesheet" type="text/css" href="public/assets/styles/custom.css">
      <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/3.5.2/animate.min.css">
   <!-- start Mixpanel -->
   <script type="text/javascript" async>(function(e,a){if(!a.__SV){var b=window;try{var c,l,i,j=b.location,g=j.hash;c=function(a,b){return(l=a.match(RegExp(b+"=([^&]*)")))?l[1]:null};g&&c(g,"state")&&(i=JSON.parse(decodeURIComponent(c(g,"state"))),"mpeditor"===i.action&&(b.sessionStorage.setItem("_mpcehash",g),history.replaceState(i.desiredHash||"",e.title,j.pathname+j.search)))}catch(m){}var k,h;window.mixpanel=a;a._i=[];a.init=function(b,c,f){function e(b,a){var c=a.split(".");2==c.length&&(b=b[c[0]],a=c[1]);b[a]=function(){b.push([a].concat(Array.prototype.slice.call(arguments,
   0)))}}var d=a;"undefined"!==typeof f?d=a[f]=[]:f="mixpanel";d.people=d.people||[];d.toString=function(b){var a="mixpanel";"mixpanel"!==f&&(a+="."+f);b||(a+=" (stub)");return a};d.people.toString=function(){return d.toString(1)+".people (stub)"};k="disable time_event track track_pageview track_links track_forms register register_once alias unregister identify name_tag set_config reset people.set people.set_once people.increment people.append people.union people.track_charge people.clear_charges people.delete_user".split(" ");
   for(h=0;h<k.length;h++)e(d,k[h]);a._i.push([b,c,f])};a.__SV=1.2;b=e.createElement("script");b.type="text/javascript";b.async=!0;b.src="undefined"!==typeof MIXPANEL_CUSTOM_LIB_URL?MIXPANEL_CUSTOM_LIB_URL:"file:"===e.location.protocol&&"//cdn.mxpnl.com/libs/mixpanel-2-latest.min.js".match(/^\/\//)?"https://cdn.mxpnl.com/libs/mixpanel-2-latest.min.js":"//cdn.mxpnl.com/libs/mixpanel-2-latest.min.js";c=e.getElementsByTagName("script")[0];c.parentNode.insertBefore(b,c)}})(document,window.mixpanel||[]);
   mixpanel.init("be05a4d590e8bc9d5f2cd6b3b3ffb78b")
   </script>
   <!-- end Mixpanel -->
   <!-- Style For the initial loader -->
   <style>
    @keyframes lds-rolling {
        0% {
          -webkit-transform: translate(-50%, -50%) rotate(0deg);
          transform: translate(-50%, -50%) rotate(0deg);
        }
        100% {
          -webkit-transform: translate(-50%, -50%) rotate(360deg);
          transform: translate(-50%, -50%) rotate(360deg);
        }
      }
      @-webkit-keyframes lds-rolling {
        0% {
          -webkit-transform: translate(-50%, -50%) rotate(0deg);
          transform: translate(-50%, -50%) rotate(0deg);
        }
        100% {
          -webkit-transform: translate(-50%, -50%) rotate(360deg);
          transform: translate(-50%, -50%) rotate(360deg);
        }
      }
      .lds-rolling {
        position: relative;
      }
      .lds-rolling div,
      .lds-rolling div:after {
        width: 25%;
        height: 25%;
        border: 5px solid #2196f3;
        border-top-color: transparent;
        border-radius: 50%;
      }
      .lds-rolling div {
        -webkit-animation: lds-rolling 1s linear infinite;
        animation: lds-rolling 1s linear infinite;
        top: 100px;
        left: 100px;
      }
      .lds-rolling div:after {
        -webkit-transform: rotate(90deg);
        transform: rotate(90deg);
      }
      .lds-rolling {
        width: 200px !important;
        height: 200px !important;
        -webkit-transform: translate(-100px, -100px) scale(1) translate(100px, 100px);
        transform: translate(-100px, -100px) scale(1) translate(100px, 100px);
      }
      .loader{
        position: absolute;
        width:25%;
        height:25%;
        top:47%;
        left:50%;
      }
    </style>
   </head>

   <body>
      <div id="initialLoader" class="loader">
          <div class="lds-css ng-scope"><div style="width:100%;height:100%" class="lds-rolling"><div></div></div>
        </div>
      </div>
        <div id="main"><%- markup -%>
        </div>
      <script type="text/javascript" src="https://js.stripe.com/v2/"></script>
      <script>
          CONFIG = <%- config %>;

        for(let key in CONFIG) {
            window[key] = CONFIG[key];
        }
        Stripe.setPublishableKey(STRIPE_KEY);
      </script>
      <script src = "public/client.min.js"></script>
   </body>

</html>