{"name": "ezybackend-acl", "version": "1.1.1", "description": "", "main": "./src/ACL.js", "scripts": {"client": "webpack --watch --progress"}, "repository": {"type": "git", "url": "git+https://github.com/EzyBackend/ACL.git"}, "author": "Cloudboost", "license": "ISC", "bugs": {"url": "https://github.com/EzyBackend/ACL/issues"}, "homepage": "https://github.com/EzyBackend/ACL#readme", "devDependencies": {"babel-core": "^6.7.7", "babel-loader": "^6.2.4", "babel-plugin-transform-class-properties": "^6.16.0", "babel-plugin-transform-decorators-legacy": "^1.3.4", "babel-preset-es2015": "^6.6.0", "babel-preset-react": "^6.5.0", "babel-preset-stage-0": "^6.16.0", "babel-preset-stage-1": "^6.16.0", "webpack": "^2.2.1", "babel-cli": "^6.18.0", "css-loader": "^0.26.1", "style-loader": "^0.13.1", "react": "^15.0.0", "react-dom": "^15.0.0", "material-ui": "^0.16.0", "react-bootstrap": "^0.30.7"}, "peerDependencies": {"react": "^15.0.0", "react-dom": "^15.0.0", "material-ui": "^0.16.0", "react-bootstrap": "^0.30.7"}}