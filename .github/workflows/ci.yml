name: EzyBackend CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

env:
  NODE_VERSION: '20'
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

jobs:
  # Security and Dependency Audit
  security-audit:
    name: Security Audit
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Audit data-service dependencies
        run: |
          cd data-service
          npm ci
          npm audit --audit-level=moderate

      - name: Audit user-service dependencies
        run: |
          cd user-service
          npm ci
          npm audit --audit-level=moderate

      - name: Audit dashboard-ui dependencies
        run: |
          cd dashboard-ui
          npm ci
          npm audit --audit-level=moderate

  # Code Quality and Linting
  code-quality:
    name: Code Quality
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Lint data-service
        run: |
          cd data-service
          npm ci
          npm run lint-check
          npm run format-check

      - name: Lint user-service
        run: |
          cd user-service
          npm ci
          npm run lint-check
          npm run format-check

      - name: Lint dashboard-ui
        run: |
          cd dashboard-ui
          npm ci
          npm run lint-check
          npm run format-check

  # Unit Tests
  test:
    name: Unit Tests
    runs-on: ubuntu-latest
    services:
      mongodb:
        image: mongo:7.0
        env:
          MONGO_INITDB_ROOT_USERNAME: test
          MONGO_INITDB_ROOT_PASSWORD: test
        ports:
          - 27017:27017
        options: >-
          --health-cmd "mongosh --eval 'db.adminCommand(\"ping\")'"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

      redis:
        image: redis:7-alpine
        ports:
          - 6379:6379
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Test data-service
        env:
          MONGODB_URL: ****************************************
          REDIS_URL: redis://localhost:6379
          NODE_ENV: test
        run: |
          cd data-service
          npm ci
          npm test

      - name: Test user-service
        env:
          MONGODB_URL: ****************************************
          REDIS_URL: redis://localhost:6379
          NODE_ENV: test
        run: |
          cd user-service
          npm ci
          npm test

      - name: Test dashboard-ui
        run: |
          cd dashboard-ui
          npm ci
          npm test

      - name: Upload coverage reports
        uses: codecov/codecov-action@v3
        with:
          directory: ./coverage
          flags: unittests
          name: codecov-umbrella

  # Build and Test Docker Images
  build:
    name: Build Docker Images
    runs-on: ubuntu-latest
    needs: [security-audit, code-quality, test]
    strategy:
      matrix:
        service: [data-service, user-service, dashboard-ui, home-ui]
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Log in to Container Registry
        if: github.event_name != 'pull_request'
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Extract metadata
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}/${{ matrix.service }}
          tags: |
            type=ref,event=branch
            type=ref,event=pr
            type=sha,prefix={{branch}}-
            type=raw,value=latest,enable={{is_default_branch}}

      - name: Build and push Docker image
        uses: docker/build-push-action@v5
        with:
          context: ./${{ matrix.service }}
          platforms: linux/amd64,linux/arm64
          push: ${{ github.event_name != 'pull_request' }}
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          cache-from: type=gha
          cache-to: type=gha,mode=max

  # Container Security Scanning
  container-scan:
    name: Container Security Scan
    runs-on: ubuntu-latest
    needs: [build]
    if: github.event_name != 'pull_request'
    strategy:
      matrix:
        service: [data-service, user-service, dashboard-ui]
    steps:
      - name: Run Trivy vulnerability scanner
        uses: aquasecurity/trivy-action@master
        with:
          image-ref: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}/${{ matrix.service }}:${{ github.sha }}
          format: 'sarif'
          output: 'trivy-results-${{ matrix.service }}.sarif'

      - name: Upload Trivy scan results to GitHub Security tab
        uses: github/codeql-action/upload-sarif@v2
        with:
          sarif_file: 'trivy-results-${{ matrix.service }}.sarif'

  # Integration Tests
  integration-test:
    name: Integration Tests
    runs-on: ubuntu-latest
    needs: [build]
    if: github.event_name != 'pull_request'
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Start services with Docker Compose
        run: |
          cp .env.example .env
          docker-compose up -d --wait
          sleep 30

      - name: Run health checks
        run: |
          curl -f http://localhost:4730/health || exit 1
          curl -f http://localhost:3000/health || exit 1
          curl -f http://localhost:1440/health || exit 1

      - name: Run integration tests
        run: |
          cd smoketest
          npm ci
          npm test

      - name: Cleanup
        if: always()
        run: docker-compose down -v

  # Deploy to Staging
  deploy-staging:
    name: Deploy to Staging
    runs-on: ubuntu-latest
    needs: [integration-test]
    if: github.ref == 'refs/heads/develop' && github.event_name == 'push'
    environment: staging
    steps:
      - name: Deploy to staging
        run: |
          echo "Deploying to staging environment..."
          # Add your staging deployment commands here

  # Deploy to Production
  deploy-production:
    name: Deploy to Production
    runs-on: ubuntu-latest
    needs: [integration-test]
    if: github.ref == 'refs/heads/main' && github.event_name == 'push'
    environment: production
    steps:
      - name: Deploy to production
        run: |
          echo "Deploying to production environment..."
          # Add your production deployment commands here

  # Notify on Success/Failure
  notify:
    name: Notify
    runs-on: ubuntu-latest
    needs: [deploy-staging, deploy-production]
    if: always()
    steps:
      - name: Notify Slack on success
        if: success()
        uses: 8398a7/action-slack@v3
        with:
          status: success
          text: 'EzyBackend deployment successful! 🚀'
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}

      - name: Notify Slack on failure
        if: failure()
        uses: 8398a7/action-slack@v3
        with:
          status: failure
          text: 'EzyBackend deployment failed! ❌'
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
