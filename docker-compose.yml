services:
  mongodb:
    image: mongo:6.0
    container_name: ezybackend-mongodb
    restart: unless-stopped
    environment:
      MONGO_INITDB_ROOT_USERNAME: ${MONGO_USERNAME:-ezybackend}
      MONGO_INITDB_ROOT_PASSWORD: ${MONGO_PASSWORD:-changeme}
      MONGO_INITDB_DATABASE: ${MONGO_DATABASE:-ezybackend}
    command: ["--bind_ip_all"]
    volumes:
      - mongodb_data:/data/db
    ports:
      - "27018:27017"
    networks:
      - ezybackend
    healthcheck:
      test: ["CMD-SHELL", "mongosh --quiet --eval 'db.adminCommand(\"ping\")' -u ${MONGO_USERNAME:-ezybackend} -p ${MONGO_PASSWORD:-your-secure-mongodb-password} --authenticationDatabase admin || exit 1"]
      interval: 10s
      timeout: 5s
      retries: 10
      start_period: 20s

  redis:
    image: redis:latest
    container_name: ezybackend-redis
    restart: unless-stopped
    command: ["redis-server", "--requirepass", "${REDIS_PASSWORD:-your-secure-redis-password}"]
    volumes:
      - redis_data:/data
    ports:
      - "6380:6379"
    networks:
      - ezybackend
    healthcheck:
      test: ["CMD-SHELL", "redis-cli -a ${REDIS_PASSWORD:-your-secure-redis-password} ping || exit 1"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 5s

  data-service:
    build: ./data-service
    container_name: ezybackend-data-service
    restart: unless-stopped
    environment:
      NODE_ENV: production
      PORT: 4730
      RUNNING_IN_DOCKER: "true"
      DOCKER: "true"
      MONGODB_URI: mongodb://${MONGO_USERNAME:-ezybackend}:${MONGO_PASSWORD:-your-secure-mongodb-password}@mongodb:27017/${MONGO_DATABASE:-ezybackend}
      REDIS_URL: redis://:${REDIS_PASSWORD:-your-secure-redis-password}@redis:6379
      MONGO_1_PORT_27017_TCP_ADDR: mongodb
      MONGO_1_PORT_27017_TCP_PORT: 27017
      REDIS_1_PORT_6379_TCP_ADDR: redis
      REDIS_1_PORT_6379_TCP_PORT: 6379
    ports:
      - "4731:4730"
    volumes:
      - ./data-service/.dockerenv:/root/.dockerenv:ro
    depends_on:
      mongodb:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - ezybackend

  user-service:
    image: ezybackend/user-service:latest
    container_name: ezybackend-user-service
    restart: unless-stopped
    environment:
      NODE_ENV: production
      PORT: 3000
      MONGODB_URI: mongodb://${MONGO_USERNAME:-ezybackend}:${MONGO_PASSWORD:-your-secure-mongodb-password}@mongodb:27017/${MONGO_DATABASE:-ezybackend}
      REDIS_URL: redis://:${REDIS_PASSWORD:-your-secure-redis-password}@redis:6379
    ports:
      - "3001:3000"
    depends_on:
      mongodb:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - ezybackend

  dashboard-ui:
    image: ezybackend/dashboard-ui:latest
    container_name: ezybackend-dashboard-ui
    restart: unless-stopped
    environment:
      NODE_ENV: production
      PORT: 1440
      API_URL: http://data-service:4730
    ports:
      - "1441:1440"
    depends_on:
      - data-service
    networks:
      - ezybackend

  home-ui:
    image: ezybackend/home-ui:latest
    container_name: ezybackend-home-ui
    restart: unless-stopped
    environment:
      NODE_ENV: production
      PORT: 1444
      API_URL: http://user-service:3000
    ports:
      - "1445:1444"
    depends_on:
      - user-service
    networks:
      - ezybackend

volumes:
  mongodb_data:
  redis_data:

networks:
  ezybackend:
    driver: bridge
