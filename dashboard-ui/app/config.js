/**
 * Created by Darkstar on 12/4/2016.
 */
let globals = () => {
  return {
    dashboardAPI: window.USER_SERVICE_URL,
    analyticsAPI: '',
    accountsURL: window.ACCOUNTS_URL,
    accountsAPI: window.USER_SERVICE_URL,
    ezyBackendAPI: window.SERVER_URL
  };
};

let config = globals();

config.paymentCountries = [
  { code: 'AFG', label: 'Afghanistan' },
  { code: 'ALB', label: 'Albania' },
  { code: 'DZA', label: 'Algeria' },
  { code: 'ASM', label: 'American Samoa' },
  { code: 'AND', label: 'Andorra' },
  { code: 'AGO', label: 'Angola' },
  { code: 'AIA', label: 'Anguilla' },
  { code: 'ATA', label: 'Antarctica' },
  { code: 'ATG', label: 'Antigua and Barbuda' },
  { code: 'ARG', label: 'Argentina' },
  { code: 'ARM', label: 'Armenia' },
  { code: 'ABW', label: 'Aruba' },
  { code: 'AUS', label: 'Australia' },
  { code: 'AUT', label: 'Austria' },
  { code: 'AZE', label: 'Azerbaijan' },
  { code: 'BHS', label: 'Bahamas' },
  { code: 'BHR', label: 'Bahrain' },
  { code: 'BGD', label: 'Bangladesh' },
  { code: 'BRB', label: 'Barbados' },
  { code: 'BLR', label: 'Belarus' },
  { code: 'BEL', label: 'Belgium' },
  { code: 'BLZ', label: 'Belize' },
  { code: 'BEN', label: 'Benin' },
  { code: 'BMU', label: 'Bermuda' },
  { code: 'BTN', label: 'Bhutan' },
  { code: 'BOL', label: 'Bolivia' },
  { code: 'BIH', label: 'Bosnia and Herzegovina' },
  { code: 'BWA', label: 'Botswana' },
  { code: 'BVT', label: 'Bouvet Island' },
  { code: 'BRA', label: 'Brazil' },
  { code: 'IOT', label: 'British Indian Ocean Territory' },
  { code: 'BRN', label: 'Brunei Darussalam' },
  { code: 'BGR', label: 'Bulgaria' },
  { code: 'BFA', label: 'Burkina Faso' },
  { code: 'BDI', label: 'Burundi' },
  { code: 'KHM', label: 'Cambodia' },
  { code: 'CMR', label: 'Cameroon' },
  { code: 'CAN', label: 'Canada' },
  { code: 'CPV', label: 'Cape Verde' },
  { code: 'CYM', label: 'Cayman Islands' },
  { code: 'CAF', label: 'Central African Republic' },
  { code: 'TCD', label: 'Chad' },
  { code: 'CHL', label: 'Chile' },
  { code: 'CHN', label: 'China' },
  { code: 'CXR', label: 'Christmas Island' },
  { code: 'CCK', label: 'Cocos (Keeling) Islands' },
  { code: 'COL', label: 'Colombia' },
  { code: 'COM', label: 'Comoros' },
  { code: 'COG', label: 'Congo' },
  { code: 'COD', label: 'Congo, the Democratic Republic of the' },
  { code: 'COK', label: 'Cook Islands' },
  { code: 'CRI', label: 'Costa Rica' },
  { code: 'CIV', label: 'Cote D’ivoire' },
  { code: 'HRV', label: 'Croatia (Hrvatska)' },
  { code: 'CYP', label: 'Cyprus' },
  { code: 'CZE', label: 'Czech Republic' },
  { code: 'DNK', label: 'Denmark' },
  { code: 'DJI', label: 'Djibouti' },
  { code: 'DMA', label: 'Dominica' },
  { code: 'DOM', label: 'Dominican Republic' },
  { code: 'ECU', label: 'Ecuador' },
  { code: 'EGY', label: 'Egypt' },
  { code: 'SLV', label: 'El Salvador' },
  { code: 'GNQ', label: 'Equatorial Guinea' },
  { code: 'ERI', label: 'Eritrea' },
  { code: 'EST', label: 'Estonia' },
  { code: 'ETH', label: 'Ethiopia' },
  { code: 'FLK', label: 'Falkland Islands (Malvinas)' },
  { code: 'FRO', label: 'Faroe Islands' },
  { code: 'FJI', label: 'Fiji' },
  { code: 'FIN', label: 'Finland' },
  { code: 'FRA', label: 'France' },
  { code: 'FXX', label: 'France, Metropolitan' },
  { code: 'GUF', label: 'French Guiana' },
  { code: 'PYF', label: 'French Polynesia' },
  { code: 'ATF', label: 'French Southern Territories' },
  { code: 'GAB', label: 'Gabon' },
  { code: 'GMB', label: 'Gambia' },
  { code: 'GEO', label: 'Georgia' },
  { code: 'DEU', label: 'Germany' },
  { code: 'GHA', label: 'Ghana' },
  { code: 'GIB', label: 'Gibraltar' },
  { code: 'GRC', label: 'Greece' },
  { code: 'GRL', label: 'Greenland' },
  { code: 'GRD', label: 'Grenada' },
  { code: 'GLP', label: 'Guadeloupe' },
  { code: 'GUM', label: 'Guam' },
  { code: 'GTM', label: 'Guatemala' },
  { code: 'GIN', label: 'Guinea' },
  { code: 'GNB', label: 'Guinea-Bissau' },
  { code: 'GUY', label: 'Guyana' },
  { code: 'HTI', label: 'Haiti' },
  { code: 'HMD', label: 'Heard Island and Mcdonald Islands' },
  { code: 'HND', label: 'Honduras' },
  { code: 'HKG', label: 'Hong Kong' },
  { code: 'HUN', label: 'Hungary' },
  { code: 'ISL', label: 'Iceland' },
  { code: 'IND', label: 'India' },
  { code: 'IDN', label: 'Indonesia' },
  { code: 'IRQ', label: 'Iraq' },
  { code: 'IRL', label: 'Ireland' },
  { code: 'ISR', label: 'Israel' },
  { code: 'ITA', label: 'Italy' },
  { code: 'JAM', label: 'Jamaica' },
  { code: 'JPN', label: 'Japan' },
  { code: 'JOR', label: 'Jordan' },
  { code: 'KAZ', label: 'Kazakhstan' },
  { code: 'KEN', label: 'Kenya' },
  { code: 'KIR', label: 'Kiribati' },
  { code: 'KOR', label: 'Korea, Republic of' },
  { code: 'KWT', label: 'Kuwait' },
  { code: 'KGZ', label: 'Kyrgyzstan' },
  { code: 'LAO', label: 'Lao People’s Democratic Republic' },
  { code: 'LVA', label: 'Latvia' },
  { code: 'LBN', label: 'Lebanon' },
  { code: 'LSO', label: 'Lesotho' },
  { code: 'LBR', label: 'Liberia' },
  { code: 'LBY', label: 'Libyan Arab Jamahiriya' },
  { code: 'LIE', label: 'Liechtenstein' },
  { code: 'LTU', label: 'Lithuania' },
  { code: 'LUX', label: 'Luxembourg' },
  { code: 'MAC', label: 'Macao' },
  { code: 'MKD', label: 'Macedonia, the Former Yugoslav Republic of' },
  { code: 'MDG', label: 'Madagascar' },
  { code: 'MWI', label: 'Malawi' },
  { code: 'MYS', label: 'Malaysia' },
  { code: 'MDV', label: 'Maldives' },
  { code: 'MLI', label: 'Mali' },
  { code: 'MLT', label: 'Malta' },
  { code: 'MHL', label: 'Marshall Islands' },
  { code: 'MTQ', label: 'Martinique' },
  { code: 'MRT', label: 'Mauritania' },
  { code: 'MUS', label: 'Mauritius' },
  { code: 'MYT', label: 'Mayotte' },
  { code: 'MEX', label: 'Mexico' },
  { code: 'FSM', label: 'Micronesia, Federated States of' },
  { code: 'MDA', label: 'Moldova, Republic of' },
  { code: 'MCO', label: 'Monaco' },
  { code: 'MNG', label: 'Mongolia' },
  { code: 'MNE', label: 'Montenegro' },
  { code: 'MSR', label: 'Montserrat' },
  { code: 'MAR', label: 'Morocco' },
  { code: 'MOZ', label: 'Mozambique' },
  { code: 'NAM', label: 'Namibia' },
  { code: 'NRU', label: 'Nauru' },
  { code: 'NPL', label: 'Nepal' },
  { code: 'NLD', label: 'Netherlands' },
  { code: 'ANT', label: 'Netherlands Antilles' },
  { code: 'NCL', label: 'New Caledonia' },
  { code: 'NZL', label: 'New Zealand' },
  { code: 'NIC', label: 'Nicaragua' },
  { code: 'NER', label: 'Niger' },
  { code: 'NGA', label: 'Nigeria' },
  { code: 'NIU', label: 'Niue' },
  { code: 'NFK', label: 'Norfolk Island' },
  { code: 'MNP', label: 'Northern Mariana Islands' },
  { code: 'NOR', label: 'Norway' },
  { code: 'OMN', label: 'Oman' },
  { code: 'PAK', label: 'Pakistan' },
  { code: 'PLW', label: 'Palau' },
  { code: 'PSE', label: 'Palestinian Territory, Occupied' },
  { code: 'PAN', label: 'Panama' },
  { code: 'PNG', label: 'Papua New Guinea' },
  { code: 'PRY', label: 'Paraguay' },
  { code: 'PER', label: 'Peru' },
  { code: 'PHL', label: 'Philippines' },
  { code: 'PCN', label: 'Pitcairn' },
  { code: 'POL', label: 'Poland' },
  { code: 'PRT', label: 'Portugal' },
  { code: 'PRI', label: 'Puerto Rico' },
  { code: 'QAT', label: 'Qatar' },
  { code: 'REU', label: 'Reunion' },
  { code: 'ROU', label: 'Romania' },
  { code: 'RUS', label: 'Russian Federation' },
  { code: 'RWA', label: 'Rwanda' },
  { code: 'SHN', label: 'Saint Helena' },
  { code: 'KNA', label: 'Saint Kitts and Nevis' },
  { code: 'LCA', label: 'Saint Lucia' },
  { code: 'SPM', label: 'Saint Pierre and Miquelon' },
  { code: 'VCT', label: 'Saint Vincent and the Grenadines' },
  { code: 'WSM', label: 'Samoa' },
  { code: 'SMR', label: 'San Marino' },
  { code: 'STP', label: 'Sao Tome and Principe' },
  { code: 'SAU', label: 'Saudi Arabia' },
  { code: 'SEN', label: 'Senegal' },
  { code: 'SRB', label: 'Serbia' },
  { code: 'SCG', label: 'Serbia and Montenegro' },
  { code: 'SYC', label: 'Seychelles' },
  { code: 'SLE', label: 'Sierra Leone' },
  { code: 'SGP', label: 'Singapore' },
  { code: 'SVK', label: 'Slovakia' },
  { code: 'SVN', label: 'Slovenia' },
  { code: 'SLB', label: 'Solomon Islands' },
  { code: 'SOM', label: 'Somalia' },
  { code: 'ZAF', label: 'South Africa' },
  { code: 'SGS', label: 'South Georgia and the South Sandwich Islands' },
  { code: 'ESP', label: 'Spain' },
  { code: 'LKA', label: 'Sri Lanka' },
  { code: 'SUR', label: 'Suriname' },
  { code: 'SJM', label: 'Svalbard and Jan Mayen Islands' },
  { code: 'SWZ', label: 'Swaziland' },
  { code: 'SWE', label: 'Sweden' },
  { code: 'CHE', label: 'Switzerland' },
  { code: 'TWN', label: 'Taiwan' },
  { code: 'TJK', label: 'Tajikistan' },
  { code: 'TZA', label: 'Tanzania, United Republic of' },
  { code: 'THA', label: 'Thailand' },
  { code: 'TLS', label: 'Timor-Leste' },
  { code: 'TGO', label: 'Togo' },
  { code: 'TKL', label: 'Tokelau' },
  { code: 'TON', label: 'Tonga' },
  { code: 'TTO', label: 'Trinidad and Tobago' },
  { code: 'TUN', label: 'Tunisia' },
  { code: 'TUR', label: 'Turkey' },
  { code: 'TKM', label: 'Turkmenistan' },
  { code: 'TCA', label: 'Turks and Caicos Islands' },
  { code: 'TUV', label: 'Tuvalu' },
  { code: 'UGA', label: 'Uganda' },
  { code: 'UKR', label: 'Ukraine' },
  { code: 'ARE', label: 'United Arab Emirates' },
  { code: 'GBR', label: 'United Kingdom' },
  { code: 'USA', label: 'United States' },
  { code: 'UMI', label: 'United States Minor Outlying Islands' },
  { code: 'URY', label: 'Uruguay' },
  { code: 'UZB', label: 'Uzbekistan' },
  { code: 'VUT', label: 'Vanuatu' },
  { code: 'VAT', label: 'Vatican City State (Holy See)' },
  { code: 'VEN', label: 'Venezuela' },
  { code: 'VNM', label: 'Viet Nam' },
  { code: 'VGB', label: 'Virgin Islands, British' },
  { code: 'VIR', label: 'Virgin Islands, U.S.' },
  { code: 'WLF', label: 'Wallis and Futuna Islands' },
  { code: 'ESH', label: 'Western Sahara' },
  { code: 'YEM', label: 'Yemen' },
  { code: 'YUG', label: 'Yugoslavia' },
  { code: 'ZAR', label: 'Zaire' },
  { code: 'ZMB', label: 'Zambia' },
  { code: 'ZWE', label: 'Zimbabwe' }
];

config.appSettings = {
  generalSettings: {
    category: 'general',
    settings: {
      appName: null,
      appInProduction: false,
      appIcon: null
    }
  },

  emailSettings: {
    category: 'email',
    settings: {
      mandrill: {
        apiKey: null,
        enabled: true
      },
      mailgun: {
        apiKey: null,
        domain: null,
        enabled: false
      },
      fromEmail: null,
      fromName: null

    }
  },

  pushSettings: {
    category: 'push',
    settings: {
      apple: {
        certificates: []
      },
      android: {
        credentials: []
      },
      windows: {
        credentials: []
      }
    }
  },

  authSettings: {
    category: 'auth',
    settings: {
      general: {
        enabled: true,
        callbackURL: null,
        primaryColor: '#549afc'
      },
      custom: {
        enabled: true
      },
      sessions: {
        enabled: true,
        sessionLength: 30
      },
      signupEmail: {
        enabled: false,
        allowOnlyVerifiedLogins: false,
        template: ''
      },
      resetPasswordEmail: {
        enabled: false,
        template: '',
        redirectURL: {
          enabled: false,
          URL: null
        }
      },
      facebook: {
        enabled: false,
        appId: null,
        appSecret: null,
        attributes: _getFbAttributesList(),
        permissions: _getFbPermissions()
      },
      google: {
        enabled: false,
        appId: null,
        appSecret: null,
        attributes: _getGoogleAttributesList(),
        permissions: _getGooglePermissions()
      },
      twitter: {
        enabled: false,
        appId: null,
        appSecret: null,
        attributes: null,
        permissions: null
      },
      linkedIn: {
        enabled: false,
        appId: null,
        appSecret: null,
        attributes: null,
        permissions: {
          r_basicprofile: true,
          r_emailaddress: false,
          rw_company_admin: false,
          w_share: false
        }
      },
      github: {
        enabled: false,
        appId: null,
        appSecret: null,
        attributes: {
          user: {
            enabled: true,
            scope: 'user'
          },
          userEmail: {
            enabled: true,
            scope: 'user:email'
          }
        },
        permissions: _getGithubPermissions()
      }
    }
  },

  integrationSettings: {
    category: 'integrations',
    settings: {
      general: {
        enabled: true,
        callbackURL: null,
        primaryColor: '#549afc '
      },
      slack: {
        enabled: false,
        webhook_url: '',
        access_token: '',
        oauth_response: {}
      },
      zapier: {
        enabled: false,
        webhook_url: ''
      }
    }
  }
};

module.exports = config;

// AUTH CONFIGS

function _getFbAttributesList () {
  return {
    id: true,
    about: false,
    age_range: false,
    bio: false,
    birthday: false,
    context: false,
    cover: false,
    currency: false,
    devices: false,
    education: false,
    email: false,
    favorite_athletes: false,
    favorite_teams: false,
    first_name: false,
    gender: false,
    hometown: false,
    inspirational_people: false,
    install_type: false,
    installed: false,
    interested_in: false,
    is_shared_login: false,
    is_verified: false,
    languages: false,
    last_name: false,
    link: false,
    locale: false,
    location: false,
    meeting_for: false,
    middle_name: false,
    name: false,
    name_format: false,
    payment_pricepoints: false,
    political: false,
    public_key: false,
    quotes: false,
    relationship_status: false,
    religion: false,
    security_settings: false,
    shared_login_upgrade_required_by: false,
    significant_other: false,
    sports: false,
    test_group: false,
    third_party_id: false,
    timezone: false,
    token_for_business: false,
    updated_time: false,
    verified: false,
    video_upload_limits: false,
    viewer_can_send_gift: false,
    website: false,
    work: false
  };
}

function _getFbPermissions () {
  return {
    public_profile: {
      enabled: true,
      scope: 'public_profile'
    },
    user_friends: {
      enabled: false,
      scope: 'user_friends'
    },
    email: {
      enabled: false,
      scope: 'email'
    },
    user_about_me: {
      enabled: false,
      scope: 'user_about_me'
    },
    'user_actions_books': {
      enabled: false,
      scope: 'user_actions.books'
    },
    'user_actions_fitness': {
      enabled: false,
      scope: 'user_actions.fitness'
    },
    'user_actions_music': {
      enabled: false,
      scope: 'user_actions.music'
    },
    'user_actions_news': {
      enabled: false,
      scope: 'user_actions.news'
    },
    'user_actions_video': {
      enabled: false,
      scope: 'user_actions.video'
    },
    user_birthday: {
      enabled: false,
      scope: 'user_birthday'
    },
    user_education_history: {
      enabled: false,
      scope: 'user_education_history'
    },
    user_events: {
      enabled: false,
      scope: 'user_events'
    },
    user_games_activity: {
      enabled: false,
      scope: 'user_games_activity'
    },
    user_hometown: {
      enabled: false,
      scope: 'user_hometown'
    },
    user_likes: {
      enabled: false,
      scope: 'user_likes'
    },
    user_location: {
      enabled: false,
      scope: 'user_location'
    },
    user_managed_groups: {
      enabled: false,
      scope: 'user_managed_groups'
    },
    user_photos: {
      enabled: false,
      scope: 'user_photos'
    },
    user_posts: {
      enabled: false,
      scope: 'user_posts'
    },
    user_relationships: {
      enabled: false,
      scope: 'user_relationships'
    },
    user_relationship_details: {
      enabled: false,
      scope: 'user_relationship_details'
    },
    user_religion_politics: {
      enabled: false,
      scope: 'user_religion_politics'
    },
    user_tagged_places: {
      enabled: false,
      scope: 'user_tagged_places'
    },
    user_videos: {
      enabled: false,
      scope: 'user_videos'
    },
    user_website: {
      enabled: false,
      scope: 'user_website'
    },
    user_work_history: {
      enabled: false,
      scope: 'user_work_history'
    },
    read_custom_friendlists: {
      enabled: false,
      scope: 'read_custom_friendlists'
    },
    read_insights: {
      enabled: false,
      scope: 'read_insights'
    },
    read_audience_network_insights: {
      enabled: false,
      scope: 'read_audience_network_insights'
    },
    read_page_mailboxes: {
      enabled: false,
      scope: 'read_page_mailboxes'
    },
    manage_pages: {
      enabled: false,
      scope: 'manage_pages'
    },
    publish_pages: {
      enabled: false,
      scope: 'publish_pages'
    },
    publish_actions: {
      enabled: false,
      scope: 'publish_actions'
    },
    rsvp_event: {
      enabled: false,
      scope: 'rsvp_event'
    },
    pages_show_list: {
      enabled: false,
      scope: 'pages_show_list'
    },
    pages_manage_cta: {
      enabled: false,
      scope: 'pages_manage_cta'
    },
    pages_manage_instant_articles: {
      enabled: false,
      scope: 'pages_manage_instant_articles'
    },
    ads_read: {
      enabled: false,
      scope: 'ads_read'
    },
    ads_management: {
      enabled: false,
      scope: 'ads_management'
    }
  };
}

function _getGoogleAttributesList () {
  return {
    'userinfoProfile': {
      enabled: true,
      scope: 'https://www.googleapis.com/auth/userinfo.profile'
    },
    'userinfoEmail': {
      enabled: true,
      scope: 'https://www.googleapis.com/auth/userinfo.email'
    }
  };
}

function _getGooglePermissions () {
  return {
    'contacts': {
      enabled: false,
      scope: 'https://www.googleapis.com/auth/contacts'
    },
    'blogger': {
      enabled: false,
      scope: 'https://www.googleapis.com/auth/blogger'
    },
    'calendar': {
      enabled: false,
      scope: 'https://www.googleapis.com/auth/calendar'
    },
    'gmail': {
      enabled: false,
      scope: 'https://mail.google.com/'
    },
    'googlePlus': {
      enabled: false,
      scope: 'https://www.googleapis.com/auth/plus.login'
    },
    'youtube': {
      enabled: false,
      scope: 'https://www.googleapis.com/auth/youtube'
    },
    'books': {
      enabled: false,
      scope: 'https://www.googleapis.com/auth/books'
    },
    'drive': {
      enabled: false,
      scope: 'https://www.googleapis.com/auth/drive'
    },
    'coordinates': {
      enabled: false,
      scope: 'https://www.googleapis.com/auth/coordinate'
    },
    'picasa': {
      enabled: false,
      scope: 'https://picasaweb.google.com/data/'
    },
    'spreadsheets': {
      enabled: false,
      scope: 'https://spreadsheets.google.com/feeds/'
    },
    'webmasters': {
      enabled: false,
      scope: 'https://www.googleapis.com/auth/webmasters'
    },
    'tasks': {
      enabled: false,
      scope: 'https://www.googleapis.com/auth/tasks'
    },
    'analytics': {
      enabled: false,
      scope: 'https://www.googleapis.com/auth/analytics'
    },
    'UrlShortener': {
      enabled: false,
      scope: 'https://www.googleapis.com/auth/urlshortener'
    }
  };
}

function _getGithubPermissions () {
  return {
    userFollow: {
      enabled: true,
      scope: 'user:follow'
    },
    public_repo: {
      enabled: false,
      scope: 'public_repo'
    },
    repo: {
      enabled: false,
      scope: 'repo'
    },
    repo_deployment: {
      enabled: false,
      scope: 'repo_deployment'
    },
    repoStatus: {
      enabled: false,
      scope: 'repo:status'
    },
    delete_repo: {
      enabled: false,
      scope: 'delete_repo'
    },
    notifications: {
      enabled: false,
      scope: 'notifications'
    },
    gist: {
      enabled: false,
      scope: 'gist'
    },
    readRepoHook: {
      enabled: false,
      scope: 'read:repo_hook'
    },
    writeRepoHook: {
      enabled: false,
      scope: 'write:repo_hook'
    },
    adminRepoHook: {
      enabled: false,
      scope: 'admin:repo_hook'
    },
    adminOrgHook: {
      enabled: false,
      scope: 'admin:org_hook'
    },
    readOrg: {
      enabled: false,
      scope: 'read:org'
    },
    writeOrg: {
      enabled: false,
      scope: 'write:org'
    },
    adminOrg: {
      enabled: false,
      scope: 'admin:org'
    },
    readPublicKey: {
      enabled: false,
      scope: 'read:public_key'
    },
    writePublicKey: {
      enabled: false,
      scope: 'write:public_key'
    },
    adminPublicKey: {
      enabled: false,
      scope: 'admin:public_key'
    },
    readGpgKey: {
      enabled: false,
      scope: 'read:gpg_key'
    },
    writeGpgKey: {
      enabled: false,
      scope: 'write:gpg_key'
    },
    adminGpgKey: {
      enabled: false,
      scope: 'admin:gpg_key'
    }
  };
}
