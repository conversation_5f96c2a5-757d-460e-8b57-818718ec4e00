/**
 * Created by Darkstar on 11/29/2016.
 */
import React from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import Chart from 'chart.js';

export class StorageAnalytics extends React.Component {
  static propTypes = {
    analyticsStorage: PropTypes.shape({
      usage: PropTypes.array
    }),
    appData: PropTypes.shape({
      appId: PropTypes.string
    })
  }

  constructor (props) {
    super(props);
    this.state = {
      totalSpaceUsed: 0,
      noData: true
    };
  }

  componentDidMount () {
    let StorageData = this.props.analyticsStorage.usage
      .filter(temp => (this.props.appData.appId === temp.appId))
      .map((x) => [new Date(x.timeStamp).toDateString(), x.size])
      .reverse();

    if (StorageData.length > 0) {
      this.setState(
        { noData: false, totalSpaceUsed: parseFloat(StorageData.reduce((total, element) => total + element[1], 0)).toFixed(2) },
        () => this.buildGraphs(StorageData)
      );
    }
  }

  componentWillReceiveProps (nextProps) {
    let StorageData = nextProps.analyticsStorage.usage
      .filter(temp => (nextProps.appData.appId === temp.appId))
      .map(x => [new Date(x.timeStamp).toDateString(), x.size]);

    if (StorageData.length > 0) {
      this.setState({
        noData: false,
        totalSpaceUsed: parseFloat(StorageData.reduce((total, element) => total + element[1], 0)).toFixed(2)
      }, () => this.buildGraphs(StorageData));
    }
  }

  buildGraphs (StorageData) {
    var ctx = document.getElementById('storageChart').getContext('2d');
    let labels = StorageData.map(temp => temp[0]);
    let data = StorageData.map(temp => temp[1]);

    // if there is only one label than it is showing it as single data point instead of line.
    // hence prepending 0 for data and some blank label for label
    // if there is some other solution provided by chart.js, plese <NAME_EMAIL>
    if (labels.length === 1 && data.length === 1) {
      labels.unshift(' ');
      data.unshift(0);
    }
    // eslint-disable-next-line
    var myChart = new Chart(ctx, {
      type: 'line',
      data: {
        labels: labels,
        datasets: [
          {
            label: 'Storage count ',
            data: data,
            backgroundColor: ['rgba(255, 255, 255, 0.2)'],
            borderColor: ['rgba(173,0,255,1)'],
            borderWidth: 1,
            lineTension: 0,
            fill: false
          }
        ]
      },
      options: {
        scales: {
          yAxes: [
            {
              ticks: {
                beginAtZero: true
              },
              gridLines: {
                display: false
              }
            }
          ],
          xAxes: [
            {
              gridLines: {
                display: false
              }
            }
          ]
        },
        responsive: true,
        maintainAspectRatio: false,
        layout: {
          padding: {
            left: 50,
            right: 50,
            top: 10,
            bottom: 10
          }
        }
      }
    });
  }

  render () {
    return (
      <div>
        {
          (this.state.noData === false) &&
          <div>
            <div className='flex-general-row-wrapper'
              style={{
                backgroundColor: '#fff',
                paddingLeft: 50,
                paddingRight: 10,
                paddingTop: 30,
                paddingBottom: 10
              }}
            >
              <div className='cf' style={{ height: 60 }}>
                <div className='pull-left'>
                  <div style={{ height: 60, fontSize: 82, marginTop: '-30px' }}>
                                        STORAGE
                  </div>
                </div>
              </div>
              <div className='cf' style={{ width: '40%', marginLeft: 30 }}>
                <div className='pull-left'>
                  <div style={{ height: 60, width: 5, backgroundColor: '#549afc' }} />
                </div>
                <div className='pull-left' style={{ marginLeft: 6 }}>
                  <p style={{ color: '#AAAAAA', marginTop: '-3px' }}>SPACE USED THIS MONTH</p>
                  <p style={{
                    fontSize: 48,
                    fontWeight: 700,
                    marginTop: -20
                  }}>
                    {this.state.totalSpaceUsed + ' '} <span style={{ fontSize: 27 }}>MB</span>
                  </p>
                </div>
              </div>
            </div>
            <div>
              <canvas id='storageChart' width='400' height='400' />
            </div>
          </div>
        }
        {
          (this.state.noData === true) &&
          <div style={{ width: '100%', height: '100%' }} className='flex-general-column-wrapper-center'>
            <div>
              <span style={{ fontSize: 20, color: '#D1D1D1' }}>No Storage Space Used</span>
            </div>
          </div>
        }
      </div>
    );
  }
}

const mapStateToProps = (state) => {
  return {};
};

const mapDispatchToProps = (dispatch) => {
  return {};
};

export default connect(mapStateToProps, mapDispatchToProps)(StorageAnalytics);
