machine:
  node:
    version: 7.4.0
  services:
    - docker
  environment:
    CLOUDSDK_CORE_DISABLE_PROMPTS: 1
dependencies:
  override:
    - sudo apt-get update
    - sudo apt-get install curl libc6 libcurl3 zlib1g
    - npm install
    - npm install -g webpack
  post:
    - webpack --progress
    - curl -LO https://storage.googleapis.com/kubernetes-release/release/$(curl -s https://storage.googleapis.com/kubernetes-release/release/stable.txt)/bin/linux/amd64/kubectl
    - chmod +x ./kubectl
    - sudo mv ./kubectl /usr/local/bin/kubectl
test:
  pre:
    - docker build -t ezybackend/analytics-ui:3.0.$CIRCLE_BUILD_NUM .
  override:
    - echo "NO TESTS REQUIRED FOR analytics-ui"
deployment:
  staging:
    branch: staging
    commands:
      - docker build -t ezybackend/analytics-ui:staging .
      - docker login --username $DOCKERUSERNAME --password $DOCKERPASSWORD --email $DOCKEREMAIL
      - docker push ezybackend/analytics-ui:3.0.$CIRCLE_BUILD_NUM
      - docker push ezybackend/analytics-ui:staging
      - git clone https://github.com/EzyBackend/kube-cred.git
      - cd kube-cred && openssl enc -in config_staging.enc -out config -d -aes256 -k $KUBE_ENC
      - mkdir ~/.kube
      - cd kube-cred && mv config ~/.kube/
      - kubectl rolling-update ezybackend-analytics-ui-staging --image=ezybackend/analytics-ui:staging --image-pull-policy=Always
      - curl -X DELETE "https://api.cloudflare.com/client/v4/zones/"$cloudflare_zone"/purge_cache" -H "X-Auth-Email:<EMAIL>" -H "X-Auth-Key:"$cloud_flare_key -H "Content-Type:application/json" --data "{'purge_everything':true}"
  production:
    branch: master
    commands:
      - docker build -t ezybackend/analytics-ui:latest .
      - docker login --username $DOCKERUSERNAME --password $DOCKERPASSWORD --email $DOCKEREMAIL
      - docker push ezybackend/analytics-ui:3.0.$CIRCLE_BUILD_NUM
      - docker push ezybackend/analytics-ui:latest
      - git clone https://github.com/EzyBackend/kube-cred.git
      - cd kube-cred && openssl enc -in config.enc -out config -d -aes256 -k $KUBE_ENC
      - mkdir ~/.kube
      - cd kube-cred && mv config ~/.kube/
      - kubectl rolling-update ezybackend-analytics-ui --image=ezybackend/analytics-ui:latest --image-pull-policy=Always
      - curl -X DELETE "https://api.cloudflare.com/client/v4/zones/"$cloudflare_zone"/purge_cache" -H "X-Auth-Email:<EMAIL>" -H "X-Auth-Key:"$cloud_flare_key -H "Content-Type:application/json" --data "{'purge_everything':true}"