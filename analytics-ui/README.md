<img src="https://www.dropbox.com/s/7ghf1kl7crp5bvy/CbLogoIcon.png?raw=1" height="80" />

[![Build Status](https://travis-ci.org/EzyBackend/ezybackend.svg?branch=master)](https://travis-ci.org/EzyBackend/analytics-ui)

EzyBackend is the Complete NoSQL Database Service for your app. **Think of EzyBackend as Parse + Firebase + Algolia + Iron.io all combined into one** :
 - Data-Storage / JSON Storage / BLOB Storage
 - 100% data ownership
 - Realtime
 - Search
 - Cache
 - Queues
 - More - ACL's, User Authentication, Server-less apps and more.

# EzyBackend Analytics
This is a EzyBackend Analytics UI for  managing analytics related to cloudApp and can be used with  [EzyBackend Engine. ]( https://ezybackend.io/)


# Contributing

Pull requests are very welcome!

We'd love to hear your feedback and suggestions in the issue tracker.

# Backers

If you're using EzyBackend Open Source and like what we've built, help us speed development by being a backer. [[Become a backer](https://opencollective.com/ezybackend#backer)]

<a href="https://opencollective.com/ezybackend/backer/0/website" target="_blank"><img src="https://opencollective.com/ezybackend/backer/0/avatar.svg"></a>
<a href="https://opencollective.com/ezybackend/backer/1/website" target="_blank"><img src="https://opencollective.com/ezybackend/backer/1/avatar.svg"></a>
<a href="https://opencollective.com/ezybackend/backer/2/website" target="_blank"><img src="https://opencollective.com/ezybackend/backer/2/avatar.svg"></a>
<a href="https://opencollective.com/ezybackend/backer/3/website" target="_blank"><img src="https://opencollective.com/ezybackend/backer/3/avatar.svg"></a>
<a href="https://opencollective.com/ezybackend/backer/4/website" target="_blank"><img src="https://opencollective.com/ezybackend/backer/4/avatar.svg"></a>
<a href="https://opencollective.com/ezybackend/backer/5/website" target="_blank"><img src="https://opencollective.com/ezybackend/backer/5/avatar.svg"></a>
<a href="https://opencollective.com/ezybackend/backer/6/website" target="_blank"><img src="https://opencollective.com/ezybackend/backer/6/avatar.svg"></a>
<a href="https://opencollective.com/ezybackend/backer/7/website" target="_blank"><img src="https://opencollective.com/ezybackend/backer/7/avatar.svg"></a>
<a href="https://opencollective.com/ezybackend/backer/8/website" target="_blank"><img src="https://opencollective.com/ezybackend/backer/8/avatar.svg"></a>
<a href="https://opencollective.com/ezybackend/backer/9/website" target="_blank"><img src="https://opencollective.com/ezybackend/backer/9/avatar.svg"></a>
<a href="https://opencollective.com/ezybackend/backer/10/website" target="_blank"><img src="https://opencollective.com/ezybackend/backer/10/avatar.svg"></a>


# LICENSE

Copyright 2016 HackerBay, Inc.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
