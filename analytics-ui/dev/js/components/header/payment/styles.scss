%modal-common {
    .modal-header {
        border: none;
        background-color: #549afc;
        color: white;
        height: 80px;
        padding: 21px;
    }

    .modal-footer {
        background-color: #FBFBFB;
        border-top: 1px solid #d6d6d6;
    }

    .modal-body button.close,
    .modal-footer button.close,
    .modal-header button.close {
        font-size: 40px;
        font-weight: 200;
        color: white;
    }

    .modal-title {
        font-weight: normal;
        font-size: 25px;
        float: left;
    }

    .modalicon {
        float: right;
        margin-top: -10px;
    }

    .modal-body {
        background-color: white;
        border-bottom-left-radius: 6px;
        border-bottom-right-radius: 6px;

        .nav-tabs {
            a {
                color: #555;
            }

            a:hover {
                background-color: white;
                border: none;
            }

            .active {
                background: #f6f7f8;

                a {
                    background: #f6f7f8;
                }

                a:hover {
                    border: 1px solid #ddd;
                    border-bottom-color: transparent;
                }
            }
        }

        .tab-content {
            background: #f6f7f8;
            border-bottom-left-radius: 6px;
            border-bottom-right-radius: 6px;
        }
    }

    .form-control,
    input,
    select {
        display: block;
        width: 100%;
        height: 48px;
        padding: 6px 12px;
        font-size: 14px;
        line-height: 1.42857;
        color: #555555;
        background-color: #fff;
        background-image: none;
        border-radius: 4px;
    }

    input:focus,
    select:focus {
        box-shadow: none;
    }

    .modal-footer {
        border: none;
        border-top: 1px solid #d6d6d6;
    }

    body .modal-backdrop {
        background-color: #0f1e37;
    }

    body .modal-backdrop.in {
        background-color: #0f1e37;
        opacity: 0.5;
    }

    .modal-content {
        border: 1px solid transparent;
        -moz-box-shadow: 0 4px 32px rgba(31, 32, 35, 0.07);
        -webkit-box-shadow: 0 4px 32px rgba(31, 32, 35, 0.07);
        box-shadow: 0 4px 32px rgba(31, 32, 35, 0.07);
    }

    .modal-header {
        border: none;
    }

    .btn {
        font-size: 14px !important;
        border-radius: 4px !important;
        padding: 6px 24px !important;
        letter-spacing: 0.02em !important;
    }
}

.payment-modal {
    @extend %modal-common;

    .modal-content {
        width: 90%;
    }

    .modal-body {
        padding: 0;

        .tab-content {
            padding: 20px;
            font-size: 14px;
            color: #252525;
            font-weight: 500;
            margin-top: 0;

            .tab-pane {
                padding: 20px;
            }

            #left-tabs-example-pane-third {
                padding-top: 0;
            }

            .btn {
                float: right;
            }

            .input-group {
                margin: 20px 0;

                input[disabled] {
                    background-color: #fefefe;
                }
            }

            td {
                align: center;
            }
        }
    }
}

.payment {
    display: flex;
    min-height: 355px;
    position: relative;

    .cards {
        flex: 3;
        background-color: #F2F3F7;
        position: relative;

        .cardimage {
            height: 50px;
            width: 50px;
            flex: 1;
        }

        .cardDiv {
            height: 166px;
            overflow-y: scroll;
            margin: auto;
            padding-top: 13px;
        }

        .cardnotfound {
            font-size: 80px;
            margin-top: -10px;
            color: #a0a0a0;
        }

        .addacardmessage {
            color: #a0a0a0;
            margin-top: 12px;
        }
    }

    .plans {
        flex: 2;

        .planname {
            width: 100%;
            height: 89px;
            background-color: white;
            padding: 17px 25px;
            cursor: pointer;
            margin-top: 15px;
            color: #71767C;

            .type {
                font-size: 19px;
                display: block;
            }

            .value {
                font-size: 14px;
            }

            .arrow {
                float: right;
                color: #71767C;
                font-size: 29px;
                margin-top: -26px;
                margin-right: 15px;
            }
        }

        .divlabel {
            clear: both;
            padding: 5px;
            height: 30px;
            width: 100%;
            background-color: #f8f9fb;
            padding-left: 11px;
            font-size: 14px;
            color: #9AA7BA;
            font-weight: bold;
        }

        .divdetail {
            width: 100%;
            clear: both;

            .type {
                visibility: visible;
                width: 50%;
                float: left;
                padding-left: 20px;
                color: #626c7b;
                font-size: 12px;
                padding-top: 5px;
                padding-bottom: 5px;
            }

            .value {
                width: 50%;
                float: left;
                padding-left: 20px;
                color: #4d4d4d;
                font-size: 13px;
                font-weight: 500;
                padding-top: 4px;
            }

            .disabled {
                color: #c4c4c4 !important;
            }
        }
    }

    .subheading {
        color: black;
    }

    .cardadded {
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
        background-color: white;
        width: 90%;
        margin-left: 5%;
        display: flex;
        margin-bottom: 5px;
        cursor: pointer;
        border-radius: 5px;

        .cardimage {
            width: 50px;
            flex: 1;
            padding: 5px;
        }

        .cardnumber {
            flex: 3;
            padding: 13px;
        }

        .cardname {
            flex: 3;
            padding: 13px;
        }

        .cardcvv {
            flex: 1;
            margin-right: 3px;
            height: 40px;
            margin-top: 5px;
            border: 2px solid #c1c1c1;
        }

        .cardcvv:focus {
            outline: none;
        }
    }

    .buttons {
        position: absolute;
        bottom: 0;
        width: 100%;
        padding: 21px 0;

        .purchase {
            height: 41px;
            width: 48%;
            border-radius: 2px;
            background-color: #19b698;
            color: white;
            font-size: 14px;
            font-weight: 500;
            border: none;
            margin-left: 7%;
        }

        .downgrade {
            height: 41px;
            width: 48%;
            border-radius: 2px;
            background-color: #D60A00;
            color: white;
            font-size: 14px;
            font-weight: 500;
            border: none;
            margin-left: 7%;
        }

        .addcard {
            height: 41px;
            width: 27%;
            border-radius: 2px;
            background-color: #a0a0a0;
            color: white;
            font-size: 14px;
            font-weight: 500;
            border: none;
            margin-left: 40px;
        }
    }

    .heading {
        width: 100%;
        padding: 10px 10px 10px 0;
        margin-left: 7%;
        text-align: left;

        .main {
            color: #71767C;
            font-size: 20px;
            display: block;
        }

        .sub {
            font-size: 12px;
            color: #555;
        }
    }

    .fields {
        width: 85%;
        display: flex;
        background-color: white;
        margin-left: 7%;
        margin-bottom: 21px;

        .field {
            flex: 5;
            border: none;
            padding: 5px;
        }

        .field:focus {
            outline: none;
        }

        .labels {
            flex: 1;
            padding-top: 14px;
            text-align: center;
            color: #adadad;
        }
    }

    .fieldssmall {
        width: 39% !important;
        display: flex;
        background-color: white;
        margin-left: 7%;
        float: left;
        margin-bottom: 21px;

        .field {
            flex: 5;
            border: none;
            padding: 5px;
        }

        .field:focus {
            outline: none;
        }

        .labels {
            flex: 8;
            padding-top: 14px;
            text-align: center;
            color: #adadad;
        }
    }

    .selectedcard {
        border: 1px solid #bbbbbb;
        background-color: #f8f8f8;
        transform: scale(1.01, 1.05);
    }

    .billing {
        padding-top: 15px;
        height: 357px;

        .fields {
            .labels {
                flex: 2;
            }
        }

        .fieldssmall {
            .labels {
                flex: 3;
            }
        }

        .country {
            .labels {
                flex: 5;
            }
        }
    }
}
