.popovernotifications {
    min-height: 100px;
    width: 405px;
    max-height: 500px !important;
    overflow: visible !important;
    border-radius: 7px !important;
    padding-bottom: 0
}

.popovernotifications > div {
    overflow: visible
}

.notificationemptybell {
    font-size: 65px;
    color: #eaeaea
}

.notificationemptymessage {
    visibility: visible;
    font-size: 15px;
    margin-top: -10px;
    color: #d1d1d1;
    padding-left: 15px;
    padding-right: 15px
}

.okbtnnotinv {
    visibility: visible;
    background-color: #4892F9;
    border: none;
    color: #fff;
    padding: 4px 10px;
    margin-top: 5px;
    margin-left: 36px
}

.red-dot {
    background: red;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    position: absolute;
    left: 31px;
    top: 9px
}

.unSeenNotification {
    background: #edf2fa
}

.headingpop {
    height: 40px;
    padding: 10px 10px 10px 12px;
    border-bottom: 1px solid #e0e0e0;
    margin-bottom: 0;
    font-size: 11pt;
    font-weight: 400;
    color: #333;
    text-transform: capitalize
}

.clearnotbtn {
    color: #888;
    float: right;
    width: 120px !important;
    background-color: #eee;
    border: 1px solid #e8e8e8;
    box-shadow: 0 1px 2px rgba(0, 0, 0, .2);
    outline: 0 !important;
    font-size: 10pt;
    height: 20px;
    opacity: .5
}

.clearnotbtn:hover {
    opacity: 1
}

.notification-wrap {
    overflow-y: auto;
    max-height: 460px;
    display: grid
}

.notificationdiv {
    width: 100%;
    padding: 4px 12px 4px 4px
}

.notificationdiv:last-child {
    border-bottom-left-radius: 7px;
    border-bottom-right-radius: 7px
}

.notificationdiv:hover {
    background: #eff1f5
}

.notimgcontainer {
    float: left;
    width: 60px;
    height: 60px
}

.notinvicon {
    float: left;
    width: 100%;
    font-size: 60px;
    color: #aab7c4;
    opacity: .4;
    padding-left: 7px
}

.notdatacontainer {
    float: right;
    display: inline-block;
    width: 314px;
    min-height: 60px
}

.nottextinv {
    color: #595959;
    word-break: break-all;
    float: left;
    padding: 7px 0 0 5px;
    font-size: 13px;
    line-height: 1.2;
    height: 36px;
    width: 314px
}

.noteinvbtncontainer {
    width: 100%;
    padding-left: 5px;
    height: 24px;
    bottom: 0
}

.nottimestamp {
    position: relative;
    left: 0;
    padding-top: 3px;
    margin-left: 0;
    font-size: 10px;
    color: #b4b4b4;
    float: left
}

.acceptbtnnotinv, .cancelbtnnotinv {
    float: right;
    box-shadow: 0 1px 2px rgba(0, 0, 0, .2);
    outline: 0 !important;
    font-size: 12px;
    height: 20px
}

.acceptbtnnotinv {
    background-color: #549afc;
    border: 0;
    border-radius: 5px;
    color: #fff;
    width: 66px
}

.cancelbtnnotinv {
    background-color: #fff;
    border: 1px solid #e8e8e8;
    border-radius: 5px;
    width: 66px !important;
    margin-left: 10px
}

.notebadge {
    visibility: visible;
    display: inline !important;
    padding: 14px 24px 12px 7px !important;
    float: right !important;
    margin-left: -13px !important;
    cursor: pointer;
    margin-top: 1px
}

.notebadge svg {
    color: #616161 !important
}